import { useState } from 'react';
import { motion } from 'framer-motion';
import ViaAssessment from './ViaAssessment';
import RiasecAssessment from './RiasecAssessment';
import BigFiveAssessment from './BigFiveAssessment';
import { transformAssessmentScores } from '../../utils/assessmentTransformers';

const AssessmentDemo = () => {
  const [assessmentData, setAssessmentData] = useState({
    via: {},
    riasec: {},
    bigFive: {}
  });
  const [completionStatus, setCompletionStatus] = useState({
    via: false,
    riasec: false,
    bigFive: false
  });
  const [activeTab, setActiveTab] = useState('via');

  const handleAssessmentUpdate = (type, data, isComplete) => {
    setAssessmentData(prev => ({
      ...prev,
      [type]: data
    }));
    
    setCompletionStatus(prev => ({
      ...prev,
      [type]: isComplete
    }));
  };

  const isAllComplete = completionStatus.via && completionStatus.riasec && completionStatus.bigFive;

  const handleTestTransform = () => {
    if (isAllComplete) {
      try {
        const transformedData = transformAssessmentScores(assessmentData);
        console.log('Transformed Assessment Data:', transformedData);
        alert('Check console for transformed data!');
      } catch (error) {
        console.error('Transform error:', error);
        alert('Transform error: ' + error.message);
      }
    } else {
      alert('Please complete all assessments first');
    }
  };

  const tabs = [
    { id: 'via', name: 'VIA Character Strengths', component: ViaAssessment },
    { id: 'riasec', name: 'RIASEC Holland Codes', component: RiasecAssessment },
    { id: 'bigFive', name: 'Big Five Inventory', component: BigFiveAssessment }
  ];

  return (
    <div id="assessment-demo-container" className="min-h-screen bg-slate-50 p-6">
      <div id="assessment-demo-content" className="max-w-4xl mx-auto">
        <motion.div
          id="assessment-demo-header"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 text-center"
        >
          <h1 className="text-2xl font-semibold text-slate-900 mb-2">
            Assessment Demo
          </h1>
          <p className="text-slate-600 text-sm">
            Test the assessment components and data transformation
          </p>
        </motion.div>

        {/* Tab Navigation */}
        <div id="assessment-demo-tab-navigation" className="mb-4">
          <div id="assessment-demo-tab-container" className="flex space-x-1 bg-slate-100 p-1 rounded-sm">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 py-2 px-3 rounded-sm text-xs font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-white text-slate-900 shadow-sm'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                {tab.name}
                {completionStatus[tab.id] && (
                  <span className="ml-2 text-slate-700">✓</span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Assessment Content */}
        <div id="assessment-demo-content-wrapper" className="bg-white rounded-sm shadow-sm border border-slate-200 overflow-hidden">
          {tabs.map((tab) => {
            const Component = tab.component;
            return (
              <div
                id={`assessment-demo-tab-content-${tab.id}`}
                key={tab.id}
                className={activeTab === tab.id ? 'block' : 'hidden'}
              >
                <Component
                  data={assessmentData[tab.id]}
                  onUpdate={(data, isComplete) => handleAssessmentUpdate(tab.id, data, isComplete)}
                  isActive={activeTab === tab.id}
                />
              </div>
            );
          })}
        </div>

        {/* Debug Info */}
        <div id="assessment-demo-debug-info" className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Completion Status */}
          <div id="assessment-demo-completion-status" className="bg-white p-4 rounded-sm shadow-sm border border-slate-200">
            <h3 className="text-base font-medium text-slate-900 mb-3">Completion Status</h3>
            <div id="assessment-demo-completion-list" className="space-y-2">
              {Object.entries(completionStatus).map(([key, isComplete]) => (
                <div id={`assessment-demo-completion-${key}`} key={key} className="flex items-center justify-between">
                  <span className="text-slate-600 capitalize text-sm">{key}:</span>
                  <span className={`text-sm font-medium ${isComplete ? 'text-slate-700' : 'text-slate-400'}`}>
                    {isComplete ? 'Complete' : 'Incomplete'}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Score Summary */}
          <div id="assessment-demo-score-summary" className="bg-white p-4 rounded-sm shadow-sm border border-slate-200">
            <h3 className="text-base font-medium text-slate-900 mb-3">Score Summary</h3>
            <div id="assessment-demo-score-list" className="space-y-2 text-sm">
              {Object.entries(assessmentData).map(([type, data]) => (
                <div id={`assessment-demo-score-${type}`} key={type}>
                  <span className="font-medium text-slate-700 capitalize">{type}:</span>
                  <span className="ml-2 text-slate-600">
                    {Object.keys(data).length} categories scored
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Test Transform Button */}
        <div id="assessment-demo-transform-section" className="mt-6 text-center">
          <button
            onClick={handleTestTransform}
            disabled={!isAllComplete}
            className={`px-5 py-2 rounded-sm text-sm font-medium transition-all ${
              isAllComplete
                ? 'bg-slate-700 hover:bg-slate-800 text-white'
                : 'bg-slate-300 text-slate-500 cursor-not-allowed'
            }`}
          >
            Test Data Transformation
          </button>
          {!isAllComplete && (
            <p className="text-xs text-slate-600 mt-2">
              Complete all assessments to test transformation
            </p>
          )}
        </div>

        {/* Raw Data Display */}
        <div id="assessment-demo-raw-data" className="mt-6 bg-white p-4 rounded-sm shadow-sm border border-slate-200">
          <h3 className="text-base font-medium text-slate-900 mb-3">Raw Assessment Data</h3>
          <pre className="text-xs bg-slate-50 p-3 rounded-sm overflow-auto max-h-96">
            {JSON.stringify(assessmentData, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default AssessmentDemo;
