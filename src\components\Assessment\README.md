# Assessment Components

This directory contains the assessment flow components for the AI-Driven Talent Mapping system. Users can take three comprehensive assessments in one session and submit them for AI analysis.

## Components Overview

### AssessmentFlow.jsx
Main container component that orchestrates the entire assessment process.

**Features:**
- Tab-based navigation between three assessments
- Progress tracking for each assessment
- Local storage for saving progress
- Integrated submission to API
- Responsive design with progress indicators

**Props:** None (uses URL routing)

**State Management:**
- `assessmentData`: Stores scores for all three assessments
- `completionStatus`: Tracks completion status of each assessment
- `activeTab`: Currently active assessment tab
- Auto-saves progress to localStorage

### ViaAssessment.jsx
VIA Character Strengths assessment component (96 questions across 6 categories).

**Features:**
- Question-by-question navigation
- Category-based progress tracking
- Quick navigation by category
- Real-time score calculation
- Reset functionality

**Props:**
- `data`: Current assessment data
- `onUpdate(scores, isComplete)`: Callback for score updates
- `isActive`: Whether this assessment is currently active

**Scoring:**
- 7-point Likert scale (1-7)
- Converts to 0-100 scale for API
- Calculates averages per category

### RiasecAssessment.jsx
RIASEC Holland Codes assessment component (60 questions across 6 categories).

**Features:**
- Career interest assessment
- 10 questions per RIASEC category
- Visual progress indicators
- Category-based navigation

**Props:**
- `data`: Current assessment data
- `onUpdate(scores, isComplete)`: Callback for score updates
- `isActive`: Whether this assessment is currently active

**Categories:**
- Realistic, Investigative, Artistic, Social, Enterprising, Conventional

### BigFiveAssessment.jsx
Big Five Inventory assessment component (44 questions across 5 categories).

**Features:**
- Personality trait assessment
- Handles reverse-scored questions
- Variable questions per category
- "I see myself as someone who..." format

**Props:**
- `data`: Current assessment data
- `onUpdate(scores, isComplete)`: Callback for score updates
- `isActive`: Whether this assessment is currently active

**Special Features:**
- Reverse scoring for certain questions
- Visual indicator for reverse-scored items

### AssessmentStatus.jsx
Status tracking component for submitted assessments.

**Features:**
- Real-time status polling
- Progress visualization
- Auto-redirect to results when complete
- Error handling and retry options

**Props:** None (uses URL params for jobId)

**Status Types:**
- `pending`: Assessment queued
- `processing`: AI analysis in progress
- `completed`: Analysis complete, results available
- `failed`: Error occurred during processing

## Data Flow

1. **Assessment Taking:**
   ```
   User answers questions → Component calculates scores → Updates parent state → Saves to localStorage
   ```

2. **Score Calculation:**
   ```
   Raw answers (1-7 scale) → Category averages → Convert to 0-100 scale → Transform for API
   ```

3. **Submission:**
   ```
   All assessments complete → Transform scores → Submit to API → Navigate to status page
   ```

4. **Status Tracking:**
   ```
   Poll API every 3 seconds → Update status → Redirect to results when complete
   ```

## API Integration

### Submit Assessment
```javascript
POST /api/assessment/submit
{
  "assessmentName": "AI-Driven Talent Mapping",
  "riasec": { realistic: 75, investigative: 80, ... },
  "ocean": { openness: 80, conscientiousness: 75, ... },
  "viaIs": { creativity: 80, curiosity: 85, ... }
}
```

### Check Status
```javascript
GET /api/assessment/status/:jobId
{
  "job_id": "uuid",
  "status": "processing",
  "created_at": "timestamp",
  "result_id": "uuid" // when completed
}
```

## Scoring Logic

### VIA Character Strengths
- 96 questions across 6 virtue categories
- Each category maps to multiple character strengths
- Uses weighted transformation to 24 character strengths
- Scale: 1-7 → 0-100

### RIASEC Holland Codes
- 60 questions, 10 per category
- Direct category scoring
- Scale: 1-7 → 0-100

### Big Five (OCEAN)
- 44 questions with variable distribution
- Handles reverse-scored questions
- Scale: 1-7 → 0-100 (with reverse scoring)

## Local Storage

Assessment progress is automatically saved to localStorage:

```javascript
{
  "data": {
    "via": { category_scores },
    "riasec": { category_scores },
    "bigFive": { category_scores }
  },
  "status": {
    "via": boolean,
    "riasec": boolean,
    "bigFive": boolean
  },
  "timestamp": number
}
```

## Error Handling

- Network errors during submission
- Invalid assessment data
- API validation errors
- Status polling failures
- Graceful degradation with retry options

## Responsive Design

- Mobile-first approach
- Touch-friendly navigation
- Optimized for various screen sizes
- Accessible keyboard navigation

## Usage Example

```jsx
// In App.jsx routing
<Route path="/assessment" element={
  <ProtectedRoute>
    <AssessmentFlow />
  </ProtectedRoute>
} />

<Route path="/assessment/status/:jobId" element={
  <ProtectedRoute>
    <AssessmentStatus />
  </ProtectedRoute>
} />
```

## Dependencies

- React Router for navigation
- Framer Motion for animations
- Lucide React for icons
- Tailwind CSS for styling
- Assessment data from `src/data/assessmentQuestions.js`
- Score transformers from `src/utils/assessmentTransformers.js`
- API service from `src/services/apiService.js`
