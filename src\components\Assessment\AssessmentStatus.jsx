import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, Clock, AlertCircle, ArrowLeft, Eye } from 'lucide-react';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';
import apiService from '../../services/apiService';

const AssessmentStatus = () => {
  const { jobId } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pollingInterval, setPollingInterval] = useState(null);

  useEffect(() => {
    if (jobId) {
      checkStatus();
      // Start polling for status updates
      const interval = setInterval(checkStatus, 3000); // Poll every 3 seconds
      setPollingInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    }
  }, [jobId]);

  const checkStatus = async () => {
    try {
      const response = await apiService.getAssessmentStatus(jobId);
      setStatus(response);
      setError(null);
      
      // Stop polling if job is complete or failed
      if (response.status === 'completed' || response.status === 'failed') {
        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingInterval(null);
        }
      }
    } catch (error) {
      console.error('Error checking assessment status:', error);
      setError(error.response?.data?.message || 'Failed to check assessment status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusConfig = () => {
    if (!status) return null;

    switch (status.status) {
      case 'pending':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          title: 'Assessment Queued',
          description: 'Your assessment is in the queue and will be processed shortly.'
        };
      case 'processing':
        return {
          icon: LoadingSpinner,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          title: 'Processing Assessment',
          description: 'AI is analyzing your responses and generating insights.'
        };
      case 'completed':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: 'Assessment Complete',
          description: 'Your assessment has been successfully analyzed!'
        };
      case 'failed':
        return {
          icon: AlertCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Assessment Failed',
          description: 'There was an error processing your assessment.'
        };
      default:
        return {
          icon: Clock,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Unknown Status',
          description: 'Assessment status is unknown.'
        };
    }
  };

  const handleViewResults = () => {
    if (status?.result_id) {
      navigate(`/results/${status.result_id}`);
    }
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  const handleRetryAssessment = () => {
    navigate('/assessment');
  };

  if (loading && !status) {
    return (
      <div id="assessment-status-loading" className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div id="assessment-status-loading-content" className="text-center">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-slate-600 text-sm">Checking assessment status...</p>
        </div>
      </div>
    );
  }

  if (error && !status) {
    return (
      <div id="assessment-status-error" className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div id="assessment-status-error-content" className="max-w-md mx-auto p-6">
          <ErrorMessage message={error} />
          <div id="assessment-status-error-actions" className="mt-4 text-center">
            <button
              onClick={handleBackToDashboard}
              className="text-slate-700 hover:text-slate-900 text-sm font-medium"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  const statusConfig = getStatusConfig();

  return (
    <div id="assessment-status-container" className="min-h-screen bg-slate-50">
      <div id="assessment-status-content" className="container mx-auto px-4 py-6">
        {/* Header */}
        <motion.div
          id="assessment-status-header"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <button
            onClick={handleBackToDashboard}
            className="flex items-center text-slate-600 hover:text-slate-900 mb-3 transition-colors text-sm"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </button>

          <div id="assessment-status-title" className="text-center">
            <h1 className="text-2xl md:text-3xl font-semibold text-slate-900 mb-2">
              Assessment Status
            </h1>
            <p className="text-slate-600 text-sm">
              Job ID: {jobId}
            </p>
          </div>
        </motion.div>

        {/* Status Card */}
        <motion.div
          id="assessment-status-card-wrapper"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="max-w-2xl mx-auto"
        >
          <div id="assessment-status-card" className={`bg-white rounded-sm shadow-sm border p-6 ${statusConfig?.borderColor || 'border-slate-200'}`}>
            <div id="assessment-status-card-content" className="text-center">
              {/* Status Icon */}
              <div id="assessment-status-icon" className={`inline-flex items-center justify-center w-12 h-12 rounded-sm mb-4 ${statusConfig?.bgColor || 'bg-slate-50'}`}>
                {statusConfig?.icon && (
                  <statusConfig.icon className={`w-6 h-6 ${statusConfig.color}`} />
                )}
              </div>

              {/* Status Title */}
              <h2 id="assessment-status-title" className="text-xl font-semibold text-slate-900 mb-2">
                {statusConfig?.title || 'Processing...'}
              </h2>

              {/* Status Description */}
              <p id="assessment-status-description" className="text-slate-600 mb-4 text-sm">
                {statusConfig?.description || 'Please wait while we process your assessment.'}
              </p>

              {/* Progress Information */}
              {status && (
                <div id="assessment-status-progress-info" className="bg-slate-50 rounded-sm p-3 mb-4">
                  <div id="assessment-status-progress-grid" className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
                    <div id="assessment-status-current-status">
                      <span className="text-slate-500">Status:</span>
                      <span className="ml-2 font-medium text-slate-900 capitalize">
                        {status.status}
                      </span>
                    </div>
                    <div id="assessment-status-submitted-time">
                      <span className="text-slate-500">Submitted:</span>
                      <span className="ml-2 font-medium text-slate-900">
                        {new Date(status.created_at).toLocaleString()}
                      </span>
                    </div>
                    {status.updated_at && (
                      <div id="assessment-status-updated-time">
                        <span className="text-slate-500">Last Updated:</span>
                        <span className="ml-2 font-medium text-slate-900">
                          {new Date(status.updated_at).toLocaleString()}
                        </span>
                      </div>
                    )}
                    {status.estimated_completion && (
                      <div id="assessment-status-estimated-completion">
                        <span className="text-slate-500">Estimated Completion:</span>
                        <span className="ml-2 font-medium text-slate-900">
                          {new Date(status.estimated_completion).toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div id="assessment-status-action-buttons" className="flex flex-col sm:flex-row gap-2 justify-center">
                {status?.status === 'completed' && status?.result_id && (
                  <button
                    id="assessment-status-view-results-button"
                    onClick={handleViewResults}
                    className="inline-flex items-center px-5 py-2 bg-slate-700 hover:bg-slate-800 text-white text-sm font-medium rounded-sm transition-colors"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    View Results
                  </button>
                )}

                {status?.status === 'failed' && (
                  <button
                    id="assessment-status-retry-button"
                    onClick={handleRetryAssessment}
                    className="inline-flex items-center px-5 py-2 bg-slate-700 hover:bg-slate-800 text-white text-sm font-medium rounded-sm transition-colors"
                  >
                    Retry Assessment
                  </button>
                )}

                <button
                  id="assessment-status-back-button"
                  onClick={handleBackToDashboard}
                  className="inline-flex items-center px-5 py-2 bg-slate-200 hover:bg-slate-300 text-slate-700 text-sm font-medium rounded-sm transition-colors"
                >
                  Back to Dashboard
                </button>
              </div>

              {/* Auto-refresh indicator */}
              {(status?.status === 'pending' || status?.status === 'processing') && (
                <p id="assessment-status-auto-refresh-indicator" className="text-xs text-slate-500 mt-3">
                  This page will automatically update every 3 seconds
                </p>
              )}
            </div>
          </div>
        </motion.div>

        {/* Additional Information */}
        {status?.status === 'processing' && (
          <motion.div
            id="assessment-status-additional-info"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="max-w-2xl mx-auto mt-6"
          >
            <div id="assessment-status-processing-info" className="bg-white rounded-sm shadow-sm border border-slate-200 p-4">
              <h3 className="text-base font-medium text-slate-900 mb-3">What's happening now?</h3>
              <div id="assessment-status-processing-steps" className="space-y-2 text-xs text-slate-600">
                <div id="assessment-status-step-via" className="flex items-start">
                  <div className="w-1.5 h-1.5 bg-slate-500 rounded-full mt-1.5 mr-2 flex-shrink-0"></div>
                  <div>
                    <strong>Analyzing VIA Character Strengths:</strong> Identifying your top character strengths and virtues
                  </div>
                </div>
                <div id="assessment-status-step-riasec" className="flex items-start">
                  <div className="w-1.5 h-1.5 bg-slate-500 rounded-full mt-1.5 mr-2 flex-shrink-0"></div>
                  <div>
                    <strong>Processing RIASEC Interests:</strong> Mapping your career interests and work preferences
                  </div>
                </div>
                <div id="assessment-status-step-bigfive" className="flex items-start">
                  <div className="w-1.5 h-1.5 bg-slate-500 rounded-full mt-1.5 mr-2 flex-shrink-0"></div>
                  <div>
                    <strong>Evaluating Big Five Traits:</strong> Analyzing your personality dimensions
                  </div>
                </div>
                <div id="assessment-status-step-persona" className="flex items-start">
                  <div className="w-1.5 h-1.5 bg-slate-500 rounded-full mt-1.5 mr-2 flex-shrink-0"></div>
                  <div>
                    <strong>Generating Career Persona:</strong> Creating personalized career recommendations
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AssessmentStatus;
