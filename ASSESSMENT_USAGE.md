# Assessment System Usage Guide

## Overview

The AI-Driven Talent Mapping Assessment system allows users to take three comprehensive psychological assessments in one session:

1. **VIA Character Strengths** (96 questions) - Identifies your top character strengths and virtues
2. **RIASEC Holland Codes** (60 questions) - Maps your career interests and work preferences  
3. **Big Five Inventory** (44 questions) - Analyzes your personality dimensions

## How to Use

### 1. Starting an Assessment

Navigate to `/assessment` to begin the assessment process. You'll see:
- Progress overview showing completion status of all three tests
- Tab navigation to switch between assessments
- Auto-save functionality (progress is saved locally)

### 2. Taking the Assessments

**For each assessment:**
- Answer questions using the 7-point scale (1 = Strongly Disagree, 7 = Strongly Agree)
- Use Previous/Next buttons to navigate between questions
- Use the category navigation to jump to specific sections
- Your progress is automatically saved

**Assessment Details:**

#### VIA Character Strengths
- 96 questions across 6 virtue categories
- Categories: Wisdom & Knowledge, Courage, Humanity, Justice, Temperance, Transcendence
- Measures 24 character strengths total

#### RIASEC Holland Codes  
- 60 questions across 6 career interest areas
- Categories: Realistic, Investigative, Artistic, Social, Enterprising, Conventional
- Helps identify suitable career environments

#### Big Five Inventory
- 44 questions across 5 personality dimensions
- Categories: Openness, Conscientiousness, Extraversion, Agreeableness, Neuroticism
- Some questions are reverse-scored (marked with orange indicator)

### 3. Submitting Your Assessment

Once all three assessments are complete:
- The "Submit Assessment" button becomes enabled
- Click to submit your responses for AI analysis
- You'll be redirected to a status page

### 4. Tracking Analysis Progress

On the status page (`/assessment/status/:jobId`):
- Real-time updates on analysis progress
- Status indicators: Pending → Processing → Complete
- Automatic redirect to results when analysis is finished

### 5. Viewing Results

When analysis is complete:
- Click "View Results" to see your comprehensive report
- Results include all three assessment dimensions
- AI-generated career recommendations and insights

## API Integration

### Submit Assessment Endpoint
```
POST /api/assessment/submit
```

**Request Body:**
```json
{
  "assessmentName": "AI-Driven Talent Mapping",
  "riasec": {
    "realistic": 75,
    "investigative": 80,
    "artistic": 65,
    "social": 70,
    "enterprising": 85,
    "conventional": 60
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 75,
    "extraversion": 70,
    "agreeableness": 85,
    "neuroticism": 40
  },
  "viaIs": {
    "creativity": 80,
    "curiosity": 85,
    "judgment": 75,
    "loveOfLearning": 90,
    "perspective": 70,
    "bravery": 65,
    "perseverance": 80,
    "honesty": 85,
    "zest": 75,
    "love": 80,
    "kindness": 85,
    "socialIntelligence": 75,
    "teamwork": 80,
    "fairness": 85,
    "leadership": 70,
    "forgiveness": 75,
    "humility": 80,
    "prudence": 75,
    "selfRegulation": 80,
    "appreciationOfBeauty": 70,
    "gratitude": 85,
    "hope": 80,
    "humor": 75,
    "spirituality": 60
  }
}
```

**Response:**
```json
{
  "job_id": "uuid-string",
  "status": "pending",
  "message": "Assessment submitted successfully"
}
```

### Check Status Endpoint
```
GET /api/assessment/status/:jobId
```

**Response:**
```json
{
  "job_id": "uuid-string",
  "status": "completed",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:05:00Z",
  "result_id": "result-uuid"
}
```

## Data Transformation

The system automatically transforms raw assessment responses:

1. **Raw Responses** (1-7 scale) → **Category Scores** (0-100 scale)
2. **VIA Categories** → **24 Character Strengths** (using weighted mapping)
3. **Big Five** → **OCEAN format** (handling reverse scoring)
4. **RIASEC** → **Direct category mapping**

## Features

### Progress Saving
- Automatic save to localStorage
- Resume from where you left off
- Data persists across browser sessions

### Responsive Design
- Works on desktop, tablet, and mobile
- Touch-friendly navigation
- Optimized for various screen sizes

### Error Handling
- Network error recovery
- Validation feedback
- Retry mechanisms for failed submissions

### Accessibility
- Keyboard navigation support
- Screen reader friendly
- High contrast design elements

## Testing

### Demo Mode
Visit `/assessment/demo` to test the assessment components:
- Take sample assessments
- View raw data and transformations
- Test API integration without submitting

### Development Testing
```bash
# Run the development server
npm run dev

# Navigate to assessment pages
http://localhost:5173/assessment
http://localhost:5173/assessment/demo
```

## Troubleshooting

### Common Issues

1. **Assessment not saving progress**
   - Check localStorage permissions
   - Clear browser cache and try again

2. **Submit button disabled**
   - Ensure all three assessments are 100% complete
   - Check for any unanswered questions

3. **Status page not updating**
   - Check network connection
   - Verify API endpoint availability

4. **Results not loading**
   - Confirm analysis is complete
   - Check result_id in status response

### Error Messages

- "All assessments must be completed" - Complete all three tests
- "Failed to submit assessment" - Network or API error, try again
- "Assessment status unknown" - Invalid job ID or API error

## Support

For technical issues or questions:
1. Check browser console for error messages
2. Verify API connectivity
3. Review assessment completion status
4. Contact system administrator if issues persist
